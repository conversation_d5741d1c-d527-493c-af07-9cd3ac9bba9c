<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Moon Phases</title>
    <style>
        body {
            background: linear-gradient(135deg, #0c0f1e 0%, #1c2038 100%);
            min-height: 100vh;
            margin: 0;
            padding: 40px;
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            color: #e8e8f0;
        }

        .demo-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            text-align: center;
            color: #ffffff;
            margin-bottom: 40px;
            font-weight: 300;
            font-size: 2.5rem;
            letter-spacing: 1px;
            text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
        }

        .description {
            text-align: center;
            max-width: 700px;
            margin: 0 auto 50px;
            color: #b0b0d0;
            line-height: 1.6;
        }

        .moon-examples {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            justify-content: center;
            margin-bottom: 40px;
        }

        .moon-example {
            text-align: center;
            flex: 0 0 calc(12.5% - 30px);
            min-width: 100px;
        }

        .moon-example label {
            display: block;
            margin-top: 15px;
            color: #a0a0c8;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1.5px;
            font-weight: 500;
        }

        /* Enhanced Moon Phase Styling */
        .moon-phase {
            display: inline-block;
            position: relative;
            cursor: help;
            vertical-align: middle;
            transition: transform 0.3s ease;
        }

        .enhanced-moon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            position: relative;
            background: linear-gradient(135deg, 
                #e8e8e8 0%, 
                #d0d0d0 30%, 
                #b8b8b8 70%, 
                #a0a0a0 100%);
            box-shadow:
                0 0 25px rgba(255, 255, 255, 0.1),
                0 6px 20px rgba(0, 0, 0, 0.3),
                inset 0 3px 6px rgba(255, 255, 255, 0.7),
                inset 0 -3px 10px rgba(0, 0, 0, 0.4);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            border: 1px solid rgba(180, 180, 200, 0.3);
        }

        /* Surface texture */
        .enhanced-moon::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: 
                radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.5) 0%, transparent 25%),
                radial-gradient(circle at 70% 30%, rgba(0, 0, 0, 0.1) 0%, transparent 20%),
                radial-gradient(circle at 40% 60%, rgba(0, 0, 0, 0.08) 0%, transparent 18%),
                radial-gradient(circle at 65% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 15%);
            pointer-events: none;
            z-index: 2;
        }

        /* Shadow overlay for phases */
        .enhanced-moon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: var(--shadow-gradient, transparent);
            transform: var(--shadow-transform, translateX(0));
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1;
            box-shadow: var(--shadow-overlay, none);
        }

        /* Hover effects */
        .moon-phase:hover {
            transform: scale(1.1);
        }
        
        .moon-phase:hover .enhanced-moon {
            transform: scale(1.05);
            box-shadow:
                0 0 30px rgba(255, 255, 255, 0.2),
                0 8px 25px rgba(0, 0, 0, 0.4),
                0 0 0 3px rgba(255, 255, 255, 0.3),
                inset 0 3px 8px rgba(255, 255, 255, 0.8),
                inset 0 -3px 12px rgba(0, 0, 0, 0.5);
        }

        /* Phase-specific styles */
        .new-moon {
            --shadow-gradient: linear-gradient(135deg, 
                rgba(20, 20, 30, 0.95) 0%, 
                rgba(10, 10, 20, 0.98) 100%);
            --shadow-overlay: inset 0 0 10px rgba(0, 0, 0, 0.8);
        }

        /* Improved crescent definitions */
        .waxing-crescent {
            --shadow-gradient: linear-gradient(90deg, 
                rgba(30, 30, 40, 0.95) 0%, 
                rgba(30, 30, 40, 0.95) 90%, 
                rgba(30, 30, 40, 0) 95%);
            --shadow-overlay: inset -5px 0 8px rgba(0, 0, 0, 0.3);
        }

        .first-quarter {
            --shadow-gradient: linear-gradient(90deg, 
                rgba(30, 30, 40, 0.85) 0%, 
                rgba(30, 30, 40, 0.85) 50%, 
                transparent 50%);
            --shadow-overlay: inset -5px 0 10px rgba(0, 0, 0, 0.3);
        }

        .waxing-gibbous {
            --shadow-gradient: linear-gradient(90deg, 
                rgba(30, 30, 40, 0.7) 0%, 
                rgba(30, 30, 40, 0.4) 25%, 
                transparent 45%);
            --shadow-overlay: inset -5px 0 10px rgba(0, 0, 0, 0.2);
        }

        .full-moon {
            --shadow-gradient: transparent;
            --shadow-overlay: inset 0 0 15px rgba(255, 255, 255, 0.3);
        }

        .waning-gibbous {
            --shadow-gradient: linear-gradient(270deg, 
                rgba(30, 30, 40, 0.7) 0%, 
                rgba(30, 30, 40, 0.4) 25%, 
                transparent 45%);
            --shadow-overlay: inset 5px 0 10px rgba(0, 0, 0, 0.2);
        }

        .last-quarter {
            --shadow-gradient: linear-gradient(270deg, 
                rgba(30, 30, 40, 0.85) 0%, 
                rgba(30, 30, 40, 0.85) 50%, 
                transparent 50%);
            --shadow-overlay: inset 5px 0 10px rgba(0, 0, 0, 0.3);
        }

        .waning-crescent {
            --shadow-gradient: linear-gradient(270deg, 
                rgba(30, 30, 40, 0.95) 0%, 
                rgba(30, 30, 40, 0.95) 90%, 
                rgba(30, 30, 40, 0) 95%);
            --shadow-overlay: inset 5px 0 8px rgba(0, 0, 0, 0.3);
        }

        /* Special styling for new moon to make it more distinctly dark */
        .enhanced-moon.new-moon {
            box-shadow:
                0 0 15px rgba(0, 0, 0, 0.6),
                0 4px 15px rgba(0, 0, 0, 0.5),
                inset 0 0 8px rgba(0, 0, 0, 0.8),
                inset 0 -4px 10px rgba(0, 0, 0, 0.6);
            border: 1px solid rgba(80, 80, 100, 0.5);
        }

        .moon-phase:hover .enhanced-moon.new-moon {
            box-shadow:
                0 0 25px rgba(0, 0, 0, 0.8),
                0 6px 20px rgba(0, 0, 0, 0.6),
                0 0 0 3px rgba(100, 100, 150, 0.3),
                inset 0 0 10px rgba(0, 0, 0, 0.9),
                inset 0 -4px 12px rgba(0, 0, 0, 0.7);
        }
        
        .comparison {
            display: flex;
            gap: 40px;
            justify-content: center;
            align-items: center;
            margin-top: 60px;
            padding: 30px;
            background: rgba(30, 30, 50, 0.3);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(100, 100, 150, 0.2);
        }

        .comparison-item {
            text-align: center;
            padding: 20px;
            border-radius: 15px;
            background: rgba(20, 20, 35, 0.4);
            transition: all 0.3s ease;
        }
        
        .comparison-item:hover {
            background: rgba(30, 30, 50, 0.6);
            transform: translateY(-5px);
        }

        .comparison-item h3 {
            margin: 15px 0 5px 0;
            color: #d0d0f0;
            font-size: 16px;
            font-weight: 500;
        }
        
        .improvement-list {
            max-width: 800px;
            margin: 50px auto;
            padding: 25px;
            background: rgba(30, 30, 50, 0.3);
            border-radius: 15px;
            border: 1px solid rgba(100, 100, 150, 0.2);
        }
        
        .improvement-list h2 {
            text-align: center;
            color: #ffffff;
            margin-bottom: 25px;
            font-weight: 400;
        }
        
        .improvement-list ul {
            list-style-type: none;
            padding: 0;
        }
        
        .improvement-list li {
            padding: 12px 20px;
            margin: 10px 0;
            background: rgba(40, 40, 60, 0.4);
            border-radius: 8px;
            border-left: 3px solid #6a7bff;
        }
        
        .highlight {
            color: #6a7bff;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>Enhanced Moon Phase Designs</h1>
        
        <div class="description">
            <p>Improved moon phase visualization with realistic crescents, enhanced lighting, and detailed surface textures. Hover over each moon to see the enhanced details.</p>
        </div>
        
        <div class="moon-examples">
            <div class="moon-example">
                <div class="moon-phase">
                    <div class="enhanced-moon new-moon"></div>
                </div>
                <label>New Moon</label>
            </div>
            
            <div class="moon-example">
                <div class="moon-phase">
                    <div class="enhanced-moon waxing-crescent"></div>
                </div>
                <label>Waxing Crescent</label>
            </div>
            
            <div class="moon-example">
                <div class="moon-phase">
                    <div class="enhanced-moon first-quarter"></div>
                </div>
                <label>First Quarter</label>
            </div>
            
            <div class="moon-example">
                <div class="moon-phase">
                    <div class="enhanced-moon waxing-gibbous"></div>
                </div>
                <label>Waxing Gibbous</label>
            </div>
            
            <div class="moon-example">
                <div class="moon-phase">
                    <div class="enhanced-moon full-moon"></div>
                </div>
                <label>Full Moon</label>
            </div>
            
            <div class="moon-example">
                <div class="moon-phase">
                    <div class="enhanced-moon waning-gibbous"></div>
                </div>
                <label>Waning Gibbous</label>
            </div>
            
            <div class="moon-example">
                <div class="moon-phase">
                    <div class="enhanced-moon last-quarter"></div>
                </div>
                <label>Last Quarter</label>
            </div>
            
            <div class="moon-example">
                <div class="moon-phase">
                    <div class="enhanced-moon waning-crescent"></div>
                </div>
                <label>Waning Crescent</label>
            </div>
        </div>

        <div class="comparison">
            <div class="comparison-item">
                <div class="moon-phase">
                    <div class="enhanced-moon waxing-crescent" style="width: 120px; height: 120px;"></div>
                </div>
                <h3>Improved Crescent</h3>
            </div>
            
            <div class="comparison-item">
                <div class="moon-phase">
                    <div class="enhanced-moon waning-crescent" style="width: 120px; height: 120px;"></div>
                </div>
                <h3>Realistic Terminator</h3>
            </div>
        </div>
        
        <div class="improvement-list">
            <h2>Key Improvements to Moon Crescents</h2>
            <ul>
                <li>Adjusted gradient stops to create <span class="highlight">sharper crescent definitions</span> (90% to 95% instead of 40% to 70%)</li>
                <li>Added inner shadow effects for more <span class="highlight">realistic lighting transitions</span></li>
                <li>Increased moon size for <span class="highlight">better visibility</span> of crescent details</li>
                <li>Enhanced surface texture with <span class="highlight">more pronounced craters</span> and features</li>
                <li>Improved shadow opacity for <span class="highlight">higher contrast</span> between light and dark areas</li>
                <li>Added subtle <span class="highlight">glow effects</span> to highlight the illuminated crescent edge</li>
            </ul>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const moons = document.querySelectorAll('.moon-phase');
            
            moons.forEach(moon => {
                moon.addEventListener('mouseenter', function() {
                    const label = this.closest('.moon-example').querySelector('label');
                    label.style.color = '#ffffff';
                    label.style.textShadow = '0 0 8px rgba(255, 255, 255, 0.5)';
                });
                
                moon.addEventListener('mouseleave', function() {
                    const label = this.closest('.moon-example').querySelector('label');
                    label.style.color = '#a0a0c8';
                    label.style.textShadow = 'none';
                });
            });
        });
    </script>
</body>
</html>