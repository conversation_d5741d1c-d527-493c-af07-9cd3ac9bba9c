<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Realistic Crescent Moon Phases</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #0c0f1e 0%, #1c2038 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            color: #e8e8f0;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            width: 100%;
            background: rgba(20, 22, 40, 0.8);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(100, 100, 150, 0.2);
            backdrop-filter: blur(10px);
        }

        header {
            text-align: center;
            margin-bottom: 40px;
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 300;
            color: #ffffff;
            margin-bottom: 15px;
            letter-spacing: 1px;
            text-shadow: 0 0 15px rgba(100, 150, 255, 0.4);
        }

        .subtitle {
            color: #a0a0c8;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .moon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .moon-card {
            background: rgba(30, 32, 50, 0.6);
            border-radius: 15px;
            padding: 25px 20px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(100, 100, 150, 0.2);
        }

        .moon-card:hover {
            transform: translateY(-5px);
            background: rgba(40, 42, 60, 0.8);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .moon-card h3 {
            font-size: 1.2rem;
            font-weight: 500;
            margin-top: 20px;
            color: #d0d0f0;
        }

        .moon-card p {
            color: #9090b0;
            font-size: 0.9rem;
            margin-top: 8px;
        }

        .moon-container {
            position: relative;
            width: 120px;
            height: 120px;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .moon {
            position: relative;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(145deg, 
                #e0e0e0 0%, 
                #c8c8c8 30%, 
                #b0b0b0 70%, 
                #989898 100%);
            box-shadow:
                0 0 20px rgba(255, 255, 255, 0.1),
                0 5px 15px rgba(0, 0, 0, 0.4),
                inset 0 3px 6px rgba(255, 255, 255, 0.7),
                inset 0 -3px 10px rgba(0, 0, 0, 0.4);
            overflow: hidden;
            z-index: 2;
        }

        /* Surface texture */
        .moon::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: 
                radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.4) 0%, transparent 25%),
                radial-gradient(circle at 70% 30%, rgba(0, 0, 0, 0.1) 0%, transparent 20%),
                radial-gradient(circle at 40% 60%, rgba(0, 0, 0, 0.08) 0%, transparent 18%),
                radial-gradient(circle at 65% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 15%);
            pointer-events: none;
            z-index: 3;
        }

        /* Shadow overlay for phases */
        .moon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: var(--shadow-gradient, transparent);
            z-index: 4;
            box-shadow: var(--shadow-overlay, none);
        }

        /* Crescent shadow technique */
        .crescent-shadow {
            position: absolute;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: var(--crescent-color, rgba(20, 20, 30, 0.95));
            z-index: 1;
        }

        /* Crescent shapes using radial gradients */
        .new .crescent-shadow {
            --crescent-color: rgba(15, 15, 25, 0.98);
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.8);
        }

        .waxing-crescent .crescent-shadow {
            clip-path: circle(50% at 120% 50%);
            transform: translateX(10%);
        }

        .first-quarter .crescent-shadow {
            clip-path: circle(50% at 100% 50%);
            transform: translateX(0%);
        }

        .waxing-gibbous .crescent-shadow {
            clip-path: circle(50% at 80% 50%);
            transform: translateX(-10%);
        }

        .full .crescent-shadow {
            opacity: 0;
        }

        .waning-gibbous .crescent-shadow {
            clip-path: circle(50% at 20% 50%);
            transform: translateX(10%);
        }

        .last-quarter .crescent-shadow {
            clip-path: circle(50% at 0% 50%);
            transform: translateX(0%);
        }

        .waning-crescent .crescent-shadow {
            clip-path: circle(50% at -20% 50%);
            transform: translateX(-10%);
        }

        .comparison-section {
            background: rgba(25, 27, 45, 0.6);
            border-radius: 15px;
            padding: 30px;
            margin-top: 40px;
            border: 1px solid rgba(100, 100, 150, 0.2);
        }

        .comparison-section h2 {
            text-align: center;
            margin-bottom: 30px;
            font-weight: 400;
            color: #ffffff;
            font-size: 1.8rem;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .comparison-card {
            background: rgba(35, 37, 55, 0.7);
            border-radius: 12px;
            padding: 25px;
            text-align: center;
        }

        .comparison-card h3 {
            font-size: 1.4rem;
            margin-bottom: 20px;
            color: #d0d0f0;
            font-weight: 500;
        }

        .comparison-moon {
            width: 180px;
            height: 180px;
            margin: 0 auto 20px;
            position: relative;
        }

        .comparison-moon .moon {
            width: 150px;
            height: 150px;
        }

        .comparison-moon .crescent-shadow {
            width: 150px;
            height: 150px;
        }

        .comparison-card p {
            color: #a0a0c8;
            line-height: 1.6;
            max-width: 300px;
            margin: 0 auto;
        }

        .tech-details {
            margin-top: 40px;
            padding: 25px;
            background: rgba(25, 27, 45, 0.6);
            border-radius: 15px;
            border: 1px solid rgba(100, 100, 150, 0.2);
        }

        .tech-details h2 {
            text-align: center;
            margin-bottom: 25px;
            font-weight: 400;
            color: #ffffff;
            font-size: 1.8rem;
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .detail-card {
            background: rgba(35, 37, 55, 0.7);
            border-radius: 12px;
            padding: 25px;
            text-align: center;
        }

        .detail-card h3 {
            font-size: 1.3rem;
            color: #6a7bff;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .detail-card p {
            color: #a0a0c8;
            line-height: 1.6;
        }

        footer {
            text-align: center;
            margin-top: 40px;
            color: #9090b0;
            font-size: 0.9rem;
            padding-top: 20px;
            border-top: 1px solid rgba(100, 100, 150, 0.2);
        }

        @media (max-width: 768px) {
            .moon-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            
            .moon-container {
                width: 100px;
                height: 100px;
            }
            
            .moon {
                width: 80px;
                height: 80px;
            }
            
            .crescent-shadow {
                width: 80px;
                height: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Realistic Crescent Moon Phases</h1>
            <p class="subtitle">Finally, crescent moons that actually look like crescents! Using advanced CSS techniques to create accurate lunar phase representations.</p>
        </header>
        
        <div class="moon-grid">
            <div class="moon-card">
                <div class="moon-container">
                    <div class="moon new"></div>
                    <div class="crescent-shadow"></div>
                </div>
                <h3>New Moon</h3>
                <p>Completely dark side facing Earth</p>
            </div>
            
            <div class="moon-card">
                <div class="moon-container">
                    <div class="moon waxing-crescent"></div>
                    <div class="crescent-shadow"></div>
                </div>
                <h3>Waxing Crescent</h3>
                <p>First visible crescent after new moon</p>
            </div>
            
            <div class="moon-card">
                <div class="moon-container">
                    <div class="moon first-quarter"></div>
                    <div class="crescent-shadow"></div>
                </div>
                <h3>First Quarter</h3>
                <p>Half-illuminated, waxing phase</p>
            </div>
            
            <div class="moon-card">
                <div class="moon-container">
                    <div class="moon waxing-gibbous"></div>
                    <div class="crescent-shadow"></div>
                </div>
                <h3>Waxing Gibbous</h3>
                <p>More than half illuminated</p>
            </div>
            
            <div class="moon-card">
                <div class="moon-container">
                    <div class="moon full"></div>
                    <div class="crescent-shadow"></div>
                </div>
                <h3>Full Moon</h3>
                <p>Fully illuminated side facing Earth</p>
            </div>
            
            <div class="moon-card">
                <div class="moon-container">
                    <div class="moon waning-gibbous"></div>
                    <div class="crescent-shadow"></div>
                </div>
                <h3>Waning Gibbous</h3>
                <p>More than half illuminated</p>
            </div>
            
            <div class="moon-card">
                <div class="moon-container">
                    <div class="moon last-quarter"></div>
                    <div class="crescent-shadow"></div>
                </div>
                <h3>Last Quarter</h3>
                <p>Half-illuminated, waning phase</p>
            </div>
            
            <div class="moon-card">
                <div class="moon-container">
                    <div class="moon waning-crescent"></div>
                    <div class="crescent-shadow"></div>
                </div>
                <h3>Waning Crescent</h3>
                <p>Final crescent before new moon</p>
            </div>
        </div>
        
        <div class="comparison-section">
            <h2>Crescent Moon Comparison</h2>
            <div class="comparison-grid">
                <div class="comparison-card">
                    <h3>Traditional Approach</h3>
                    <div class="comparison-moon">
                        <div class="moon" style="
                            background: #d0d0d0;
                        ">
                            <div style="
                                position: absolute;
                                top: 0;
                                left: 0;
                                width: 100%;
                                height: 100%;
                                border-radius: 50%;
                                background: linear-gradient(90deg, 
                                    rgba(30,30,40,0.7) 0%, 
                                    rgba(30,30,40,0.4) 40%, 
                                    transparent 70%);
                            "></div>
                        </div>
                    </div>
                    <p>Gradient-based approach creates soft, indistinct crescents without the characteristic sharp curve of a true crescent moon.</p>
                </div>
                
                <div class="comparison-card">
                    <h3>Improved Crescent</h3>
                    <div class="comparison-moon">
                        <div class="moon waxing-crescent"></div>
                        <div class="crescent-shadow"></div>
                    </div>
                    <p>Using clip-path with radial gradients creates a perfect crescent shape with the distinctive curved terminator line.</p>
                </div>
            </div>
        </div>
        
        <div class="tech-details">
            <h2>How We Solved the Crescent Problem</h2>
            <div class="details-grid">
                <div class="detail-card">
                    <h3>Clip-Path Technique</h3>
                    <p>Using CSS clip-path with circle() function to create the precise crescent shape by masking a circular shadow element.</p>
                </div>
                
                <div class="detail-card">
                    <h3>Radial Positioning</h3>
                    <p>Positioning the shadow circle at different locations (120%, 100%, 80%) to create waxing and waning phases.</p>
                </div>
                
                <div class="detail-card">
                    <h3>Layered Approach</h3>
                    <p>Using two layered elements - a base moon and a shadow element - to create the 3D effect with proper lighting.</p>
                </div>
                
                <div class="detail-card">
                    <h3>Surface Texturing</h3>
                    <p>Multiple radial gradients to simulate lunar maria and craters for realistic surface detail.</p>
                </div>
            </div>
        </div>
        
        <footer>
            <p>Realistic Moon Phase Visualization | CSS Techniques | Created with Astronomical Precision</p>
        </footer>
    </div>
</body>
</html>